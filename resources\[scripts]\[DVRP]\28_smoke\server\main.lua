-- Standalone server script s automatickou registrací itemů (bez exportů)

-- Funkce pro automatickou registraci itemů do ox_inventory
local function RegisterItemsToOxInventory()
    if GetResourceState('ox_inventory') ~= 'started' then
        print("[28_smoke] CHYBA: ox_inventory není spuštěn!")
        return
    end

    local itemsToRegister = {}

    -- Přidáme cigaretové itemy
    if Config.Smoking.enabled then
        for _, item in pairs(Config.Smoking.items) do
            itemsToRegister[item.name] = {
                label = item.label,
                weight = 10,
                close = true,
                consume = 0, -- Nebudeme automaticky odebírat, uděláme to sami
                client = {
                    usetime = item.duration,
                    cancel = true,
                    disable = {
                        move = true,
                        car = true,
                        combat = true
                    }
                }
            }
        end
    end

    -- Přidáme snus itemy
    if Config.Snus.enabled then
        for _, item in pairs(Config.Snus.items) do
            itemsToRegister[item.name] = {
                label = item.label,
                weight = 5,
                close = true,
                consume = 0, -- Nebudeme automaticky odebírat, ud<PERSON>l<PERSON>me to sami
                client = {
                    usetime = item.duration,
                    cancel = true,
                    disable = {
                        move = true,
                        car = true,
                        combat = true
                    }
                }
            }
        end
    end

    -- Registrujeme všechny itemy najednou
    for itemName, itemData in pairs(itemsToRegister) do
        exports.ox_inventory:registerItem(itemName, itemData)

        if Config.Debug then
            print(string.format("[28_smoke] Registrován item: %s (%s)", itemName, itemData.label))
        end
    end

    local count = 0
    for _ in pairs(itemsToRegister) do count = count + 1 end
    print(string.format("[28_smoke] Registrováno %d itemů do ox_inventory", count))
end

-- Funkce pro registraci hooků pro zachycení použití itemů
local function RegisterInventoryHooks()
    if GetResourceState('ox_inventory') ~= 'started' then
        return
    end

    -- Hook pro zachycení použití itemů přes pravý klik
    exports.ox_inventory:registerHook('swapItems', function(payload)
        local fromSlot = payload.fromSlot
        local toSlot = payload.toSlot
        local fromInventory = payload.fromInventory
        local toInventory = payload.toInventory

        -- Kontrolujeme, jestli se item používá (pravý klik -> použít)
        if fromSlot and fromSlot.name and toInventory and string.find(toInventory, 'use') then
            local itemName = fromSlot.name
            local playerId = fromInventory

            -- Zkontrolujeme, jestli je to náš item
            local isOurItem = false

            if Config.Smoking.enabled then
                for _, item in pairs(Config.Smoking.items) do
                    if item.name == itemName then
                        isOurItem = true
                        break
                    end
                end
            end

            if not isOurItem and Config.Snus.enabled then
                for _, item in pairs(Config.Snus.items) do
                    if item.name == itemName then
                        isOurItem = true
                        break
                    end
                end
            end

            if isOurItem then
                -- Použijeme náš item
                UseItem(playerId, itemName)
                return false -- Zabráníme normálnímu přesunu
            end
        end

        return true -- Povolíme normální přesun pro ostatní itemy
    end)

    print("[28_smoke] Ox_inventory hooky registrovány")
end

-- Debug příkazy pro adminy
if Config.Debug then
    RegisterCommand('smoke_test', function(source, args)
        local playerId = source

        print(string.format("[28_smoke] Test příkaz spuštěn hráčem %d", playerId))

        -- Test notifikace
        TriggerClientEvent('chat:addMessage', playerId, {
            color = {255, 0, 0},
            multiline = true,
            args = {"[28_smoke]", "Test příkaz funguje!"}
        })
    end, false)

    RegisterCommand('smoke_armor', function(source, args)
        local playerId = source
        local amount = tonumber(args[1]) or 10

        local playerPed = GetPlayerPed(playerId)
        local currentArmor = GetPedArmour(playerPed)
        local newArmor = math.min(100, currentArmor + amount)
        SetPedArmour(playerPed, newArmor)

        TriggerClientEvent('chat:addMessage', playerId, {
            color = {0, 255, 0},
            multiline = true,
            args = {"[28_smoke]", string.format("Armor nastaven na %d", newArmor)}
        })

        print(string.format("[28_smoke] Hráč %d: Armor změněn na %d", playerId, newArmor))
    end, false)

    RegisterCommand('smoke_give', function(source, args)
        local playerId = source
        local itemName = args[1] or 'cigarette'
        local amount = tonumber(args[2]) or 1

        if GetResourceState('ox_inventory') == 'started' then
            if exports.ox_inventory:AddItem(playerId, itemName, amount) then
                TriggerClientEvent('chat:addMessage', playerId, {
                    color = {0, 255, 0},
                    multiline = true,
                    args = {"[28_smoke]", string.format("Přidán item %s x%d", itemName, amount)}
                })
            else
                TriggerClientEvent('chat:addMessage', playerId, {
                    color = {255, 0, 0},
                    multiline = true,
                    args = {"[28_smoke]", "Nepodařilo se přidat item!"}
                })
            end
        end
    end, false)
end

-- Inicializace při startu
AddEventHandler('onResourceStart', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        Wait(1000)
        RegisterItemsToOxInventory()
        RegisterInventoryHooks()

        print("[28_smoke] Script byl úspěšně načten!")
        print("[28_smoke] Podporované itemy:")

        if Config.Smoking.enabled then
            for _, item in pairs(Config.Smoking.items) do
                print(string.format("  - %s (%s) - +%d armor", item.name, item.label, item.armor))
            end
        end

        if Config.Snus.enabled then
            for _, item in pairs(Config.Snus.items) do
                print(string.format("  - %s (%s) - +%d armor", item.name, item.label, item.armor))
            end
        end

        print("[28_smoke] Itemy byly automaticky přidány do ox_inventory!")
        print("[28_smoke] ŽÁDNÉ EXPORTY POTŘEBA - vše je automatické!")
        print("[28_smoke] Použití: Pravý klik na item v inventáři -> Použít")

        if Config.Debug then
            print("[28_smoke] Debug příkazy:")
            print("  /smoke_test - Test příkaz")
            print("  /smoke_armor [amount] - Přidá armor")
            print("  /smoke_give [item] [amount] - Přidá item")
            print("  /use_cigarette, /use_doutnik, /use_snus, /use_snus_strong - Fallback příkazy")
        end
    end
end)
