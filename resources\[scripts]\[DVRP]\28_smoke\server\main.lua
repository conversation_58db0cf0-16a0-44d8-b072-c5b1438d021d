-- Standalone server script pouze pro ox_inventory (bez exportů)

-- Funkce pro registraci ox_inventory itemů
local function RegisterOxInventoryItems()
    if GetResourceState('ox_inventory') ~= 'started' then
        print("[28_smoke] CHYBA: ox_inventory není spu<PERSON>ěn!")
        return
    end

    -- Registrace cigaretových itemů
    if Config.Smoking.enabled then
        for _, item in pairs(Config.Smoking.items) do
            -- Registrujeme item jako useable v ox_inventory
            exports.ox_inventory:registerHook('createItem', function(payload)
                if payload.item == item.name then
                    return {
                        name = item.name,
                        label = item.label,
                        weight = 10,
                        close = true,
                        consume = 1,
                        client = {
                            usetime = item.duration,
                            cancel = true,
                            disable = {
                                move = true,
                                car = true,
                                combat = true
                            }
                        },
                        server = {
                            export = '28_smoke.useItem'
                        }
                    }
                end
            end)

            if Config.Debug then
                print(string.format("[28_smoke] Registrován ox_inventory item: %s", item.name))
            end
        end
    end

    -- Registrace snus itemů
    if Config.Snus.enabled then
        for _, item in pairs(Config.Snus.items) do
            -- Registrujeme item jako useable v ox_inventory
            exports.ox_inventory:registerHook('createItem', function(payload)
                if payload.item == item.name then
                    return {
                        name = item.name,
                        label = item.label,
                        weight = 5,
                        close = true,
                        consume = 1,
                        client = {
                            usetime = item.duration,
                            cancel = true,
                            disable = {
                                move = true,
                                car = true,
                                combat = true
                            }
                        },
                        server = {
                            export = '28_smoke.useItem'
                        }
                    }
                end
            end)

            if Config.Debug then
                print(string.format("[28_smoke] Registrován ox_inventory item: %s", item.name))
            end
        end
    end

    print("[28_smoke] Ox_inventory items registrovány")
end

-- Export pro ox_inventory (univerzální pro všechny itemy)
exports('useItem', function(event, item, inventory, slot, data)
    if event == 'usingItem' then
        local playerId = inventory.id
        local itemName = item.name

        -- Spustíme použití itemu
        TriggerClientEvent('28_smoke:useItem', playerId, itemName)

        if Config.Debug then
            print(string.format("[28_smoke] Hráč %d použil %s", playerId, itemName))
        end

        return true
    end
end)

-- Debug příkazy pro adminy
if Config.Debug then
    RegisterCommand('smoke_test', function(source, args)
        local playerId = source

        print(string.format("[28_smoke] Test příkaz spuštěn hráčem %d", playerId))

        -- Test notifikace
        TriggerClientEvent('chat:addMessage', playerId, {
            color = {255, 0, 0},
            multiline = true,
            args = {"[28_smoke]", "Test příkaz funguje!"}
        })
    end, false)

    RegisterCommand('smoke_armor', function(source, args)
        local playerId = source
        local amount = tonumber(args[1]) or 10

        local playerPed = GetPlayerPed(playerId)
        local currentArmor = GetPedArmour(playerPed)
        local newArmor = math.min(100, currentArmor + amount)
        SetPedArmour(playerPed, newArmor)

        TriggerClientEvent('chat:addMessage', playerId, {
            color = {0, 255, 0},
            multiline = true,
            args = {"[28_smoke]", string.format("Armor nastaven na %d", newArmor)}
        })

        print(string.format("[28_smoke] Hráč %d: Armor změněn na %d", playerId, newArmor))
    end, false)

    RegisterCommand('smoke_give', function(source, args)
        local playerId = source
        local itemName = args[1] or 'cigarette'
        local amount = tonumber(args[2]) or 1

        if GetResourceState('ox_inventory') == 'started' then
            if exports.ox_inventory:AddItem(playerId, itemName, amount) then
                TriggerClientEvent('chat:addMessage', playerId, {
                    color = {0, 255, 0},
                    multiline = true,
                    args = {"[28_smoke]", string.format("Přidán item %s x%d", itemName, amount)}
                })
            else
                TriggerClientEvent('chat:addMessage', playerId, {
                    color = {255, 0, 0},
                    multiline = true,
                    args = {"[28_smoke]", "Nepodařilo se přidat item!"}
                })
            end
        end
    end, false)
end

-- Inicializace při startu
AddEventHandler('onResourceStart', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        Wait(1000)
        RegisterOxInventoryItems()

        print("[28_smoke] Script byl úspěšně načten!")
        print("[28_smoke] Podporované itemy:")

        if Config.Smoking.enabled then
            for _, item in pairs(Config.Smoking.items) do
                print(string.format("  - %s (%s) - +%d armor", item.name, item.label, item.armor))
            end
        end

        if Config.Snus.enabled then
            for _, item in pairs(Config.Snus.items) do
                print(string.format("  - %s (%s) - +%d armor", item.name, item.label, item.armor))
            end
        end

        if Config.Debug then
            print("[28_smoke] Debug příkazy:")
            print("  /smoke_test - Test příkaz")
            print("  /smoke_armor [amount] - Přidá armor")
            print("  /smoke_give [item] [amount] - Přidá item")
        end
    end
end)
