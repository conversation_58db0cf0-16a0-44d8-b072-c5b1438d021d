-- Standalone server script s ox_inventory hooky (bez exportů, bez frameworku)

-- Funkce pro kontrolu, jest<PERSON> je item náš
local function IsOurItem(itemName)
    if Config.Smoking.enabled then
        for _, item in pairs(Config.Smoking.items) do
            if item.name == itemName then
                return true, item
            end
        end
    end

    if Config.Snus.enabled then
        for _, item in pairs(Config.Snus.items) do
            if item.name == itemName then
                return true, item
            end
        end
    end

    return false, nil
end

-- Funkce pro registraci hooků pro zachycení použití itemů
local function RegisterInventoryHooks()
    if GetResourceState('ox_inventory') ~= 'started' then
        print("[28_smoke] CHYBA: ox_inventory není spuštěn!")
        return
    end

    -- Hook pro zachycení použití itemů
    exports.ox_inventory:registerHook('swapItems', function(payload)
        local fromSlot = payload.fromSlot
        local toSlot = payload.toSlot
        local fromInventory = payload.fromInventory
        local toInventory = payload.toInventory

        -- Kontrolujeme různé způsoby použití itemů
        local isUsing = false
        local itemName = nil
        local playerId = nil

        -- Způsob 1: Pravý klik -> použít (toInventory obsahuje "use")
        if fromSlot and fromSlot.name and toInventory and (toInventory == 'use' or string.find(toInventory, 'use')) then
            itemName = fromSlot.name
            playerId = fromInventory
            isUsing = true
        end

        -- Způsob 2: Drag & drop na "use" slot
        if not isUsing and fromSlot and fromSlot.name and toSlot and toSlot.name == 'use' then
            itemName = fromSlot.name
            playerId = fromInventory
            isUsing = true
        end

        if isUsing and itemName and playerId then
            local isOurItem, itemData = IsOurItem(itemName)

            if isOurItem then
                -- Použijeme náš item
                UseItem(playerId, itemName, itemData)
                return false -- Zabráníme normálnímu přesunu
            end
        end

        return true -- Povolíme normální přesun pro ostatní itemy
    end)

    print("[28_smoke] Ox_inventory hooky registrovány")
end

-- Funkce pro použití itemů (standalone bez frameworku)
local function UseItem(source, itemName, itemData)
    if not itemData then
        if Config.Debug then
            print(string.format("[28_smoke] Neznámý item: %s", itemName))
        end
        return false
    end

    -- Odebereme item z inventáře
    if exports.ox_inventory:RemoveItem(source, itemName, 1) then
        -- Spustíme použití na clientu
        TriggerClientEvent('28_smoke:useItem', source, itemName)

        if Config.Debug then
            print(string.format("[28_smoke] Hráč %d použil %s (+%d armor)", source, itemName, itemData.armor))
        end

        return true
    else
        if Config.Debug then
            print(string.format("[28_smoke] Nepodařilo se odebrat item %s hráči %d", itemName, source))
        end
        return false
    end
end

-- Registrace příkazů pro použití itemů (fallback)
RegisterCommand('use_cigarette', function(source, args)
    local isOurItem, itemData = IsOurItem('cigarette')
    if isOurItem then
        UseItem(source, 'cigarette', itemData)
    end
end, false)

RegisterCommand('use_doutnik', function(source, args)
    local isOurItem, itemData = IsOurItem('doutnik')
    if isOurItem then
        UseItem(source, 'doutnik', itemData)
    end
end, false)

RegisterCommand('use_snus', function(source, args)
    local isOurItem, itemData = IsOurItem('snus')
    if isOurItem then
        UseItem(source, 'snus', itemData)
    end
end, false)

RegisterCommand('use_snus_strong', function(source, args)
    local isOurItem, itemData = IsOurItem('snus_strong')
    if isOurItem then
        UseItem(source, 'snus_strong', itemData)
    end
end, false)

-- Event pro použití itemů z clientu
RegisterNetEvent('28_smoke:serverUseItem', function(itemName)
    local source = source
    local isOurItem, itemData = IsOurItem(itemName)
    if isOurItem then
        UseItem(source, itemName, itemData)
    end
end)

-- Debug příkazy pro adminy
if Config.Debug then
    RegisterCommand('smoke_test', function(source, args)
        local playerId = source

        print(string.format("[28_smoke] Test příkaz spuštěn hráčem %d", playerId))

        -- Test notifikace
        TriggerClientEvent('chat:addMessage', playerId, {
            color = {255, 0, 0},
            multiline = true,
            args = {"[28_smoke]", "Test příkaz funguje!"}
        })
    end, false)

    RegisterCommand('smoke_armor', function(source, args)
        local playerId = source
        local amount = tonumber(args[1]) or 10

        local playerPed = GetPlayerPed(playerId)
        local currentArmor = GetPedArmour(playerPed)
        local newArmor = math.min(100, currentArmor + amount)
        SetPedArmour(playerPed, newArmor)

        TriggerClientEvent('chat:addMessage', playerId, {
            color = {0, 255, 0},
            multiline = true,
            args = {"[28_smoke]", string.format("Armor nastaven na %d", newArmor)}
        })

        print(string.format("[28_smoke] Hráč %d: Armor změněn na %d", playerId, newArmor))
    end, false)

    RegisterCommand('smoke_give', function(source, args)
        local playerId = source
        local itemName = args[1] or 'cigarette'
        local amount = tonumber(args[2]) or 1

        if GetResourceState('ox_inventory') == 'started' then
            if exports.ox_inventory:AddItem(playerId, itemName, amount) then
                TriggerClientEvent('chat:addMessage', playerId, {
                    color = {0, 255, 0},
                    multiline = true,
                    args = {"[28_smoke]", string.format("Přidán item %s x%d", itemName, amount)}
                })
            else
                TriggerClientEvent('chat:addMessage', playerId, {
                    color = {255, 0, 0},
                    multiline = true,
                    args = {"[28_smoke]", "Nepodařilo se přidat item!"}
                })
            end
        end
    end, false)
end

-- Inicializace při startu
AddEventHandler('onResourceStart', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        Wait(2000) -- Počkáme na načtení ox_inventory
        RegisterInventoryHooks()

        print("[28_smoke] Standalone script byl úspěšně načten!")
        print("[28_smoke] Podporované itemy:")

        if Config.Smoking.enabled then
            for _, item in pairs(Config.Smoking.items) do
                print(string.format("  - %s (%s) - +%d armor", item.name, item.label, item.armor))
            end
        end

        if Config.Snus.enabled then
            for _, item in pairs(Config.Snus.items) do
                print(string.format("  - %s (%s) - +%d armor", item.name, item.label, item.armor))
            end
        end

        print("[28_smoke] ŽÁDNÉ EXPORTY POTŘEBA - vše je automatické!")
        print("[28_smoke] Použití: Pravý klik na item v inventáři -> Použít")

        if Config.Debug then
            print("[28_smoke] Debug příkazy:")
            print("  /smoke_test - Test příkaz")
            print("  /smoke_armor [amount] - Přidá armor")
            print("  /smoke_give [item] [amount] - Přidá item")
            print("  /use_cigarette, /use_doutnik, /use_snus, /use_snus_strong - Fallback příkazy")
        end
    end
end)
