local QBCore = exports['qb-core']:GetCoreObject()

-- Funkce pro aplikaci efektů
local function ApplyEffects(source, effects, type)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then return end
    
    local playerPed = GetPlayerPed(source)
    
    -- Aplikace zdraví
    if effects.health then
        local currentHealth = GetEntityHealth(playerPed)
        local newHealth = math.max(0, math.min(200, currentHealth + effects.health))
        SetEntityHealth(playerPed, newHealth)
        
        if Config.Debug then
            print(string.format("[28_smoke] Hráč %s: Zdraví změněno o %d (nové: %d)", 
                  Player.PlayerData.name, effects.health, newHealth))
        end
    end
    
    -- Aplikace armoru
    if effects.armor then
        local currentArmor = GetPedArmour(playerPed)
        local newArmor = math.max(0, math.min(100, currentArmor + effects.armor))
        SetPedArmour(playerPed, newArmor)
        
        if Config.Debug then
            print(string.format("[28_smoke] Hráč %s: Armor změn<PERSON>n o %d (nový: %d)", 
                  Player.PlayerData.name, effects.armor, newArmor))
        end
    end
    
    -- Aplikace stresu (pokud máte stress systém)
    if effects.stress and Player.PlayerData.metadata.stress then
        local currentStress = Player.PlayerData.metadata.stress
        local newStress = math.max(0, math.min(100, currentStress + effects.stress))
        Player.Functions.SetMetaData("stress", newStress)
        
        if Config.Debug then
            print(string.format("[28_smoke] Hráč %s: Stress změněn o %d (nový: %d)", 
                  Player.PlayerData.name, effects.stress, newStress))
        end
    end
    
    -- Aplikace žízně
    if effects.thirst and Player.PlayerData.metadata.thirst then
        local currentThirst = Player.PlayerData.metadata.thirst
        local newThirst = math.max(0, math.min(100, currentThirst + effects.thirst))
        Player.Functions.SetMetaData("thirst", newThirst)
        
        if Config.Debug then
            print(string.format("[28_smoke] Hráč %s: Žízeň změněna o %d (nová: %d)", 
                  Player.PlayerData.name, effects.thirst, newThirst))
        end
    end
    
    -- Aplikace hladu
    if effects.hunger and Player.PlayerData.metadata.hunger then
        local currentHunger = Player.PlayerData.metadata.hunger
        local newHunger = math.max(0, math.min(100, currentHunger + effects.hunger))
        Player.Functions.SetMetaData("hunger", newHunger)
        
        if Config.Debug then
            print(string.format("[28_smoke] Hráč %s: Hlad změněn o %d (nový: %d)", 
                  Player.PlayerData.name, effects.hunger, newHunger))
        end
    end
    
    -- Aplikace staminy (custom implementace)
    if effects.stamina then
        -- Toto je příklad - můžete implementovat vlastní stamina systém
        TriggerClientEvent('28_smoke:updateStamina', source, effects.stamina)
        
        if Config.Debug then
            print(string.format("[28_smoke] Hráč %s: Stamina změněna o %d", 
                  Player.PlayerData.name, effects.stamina))
        end
    end
end

-- Event pro aplikaci efektů
RegisterNetEvent('28_smoke:applyEffects', function(effects, type)
    local src = source
    ApplyEffects(src, effects, type)
end)

-- Funkce pro registraci itemů v ox_inventory
local function RegisterItems()
    if GetResourceState('ox_inventory') == 'started' then
        -- Registrace cigaretových itemů
        if Config.Smoking.enabled then
            for _, item in pairs(Config.Smoking.items) do
                exports.ox_inventory:registerHook('swapItems', function(payload)
                    if payload.toSlot.name == item.name then
                        return false
                    end
                end, {
                    print = false,
                    itemFilter = {
                        [item.name] = true
                    }
                })
            end
        end
        
        -- Registrace snus itemů
        if Config.Snus.enabled then
            for _, item in pairs(Config.Snus.items) do
                exports.ox_inventory:registerHook('swapItems', function(payload)
                    if payload.toSlot.name == item.name then
                        return false
                    end
                end, {
                    print = false,
                    itemFilter = {
                        [item.name] = true
                    }
                })
            end
        end
    end
end

-- Registrace použití itemů
local function RegisterItemUsage()
    if Config.Smoking.enabled then
        for _, item in pairs(Config.Smoking.items) do
            QBCore.Functions.CreateUseableItem(item.name, function(source, item)
                local Player = QBCore.Functions.GetPlayer(source)
                if not Player then return end
                
                TriggerClientEvent('28_smoke:useCigarette', source, item.name)
                Player.Functions.RemoveItem(item.name, 1)
            end)
        end
    end
    
    if Config.Snus.enabled then
        for _, item in pairs(Config.Snus.items) do
            QBCore.Functions.CreateUseableItem(item.name, function(source, item)
                local Player = QBCore.Functions.GetPlayer(source)
                if not Player then return end
                
                TriggerClientEvent('28_smoke:useSnus', source, item.name)
                Player.Functions.RemoveItem(item.name, 1)
            end)
        end
    end
end

-- Inicializace při startu
AddEventHandler('onResourceStart', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        Wait(1000) -- Počkáme na načtení ostatních zdrojů
        RegisterItems()
        RegisterItemUsage()
        
        if Config.Debug then
            print("[28_smoke] Script byl úspěšně načten!")
        end
    end
end)

-- Debug příkazy (pouze pro adminy)
if Config.Debug then
    RegisterCommand('smoke_give', function(source, args)
        local Player = QBCore.Functions.GetPlayer(source)
        if not Player then return end
        
        -- Kontrola admin práv
        if not QBCore.Functions.HasPermission(source, 'admin') then
            return
        end
        
        local itemName = args[1] or 'cigarette'
        local amount = tonumber(args[2]) or 1
        
        Player.Functions.AddItem(itemName, amount)
        TriggerClientEvent('inventory:client:ItemBox', source, QBCore.Shared.Items[itemName], "add")
    end, false)
    
    RegisterCommand('smoke_effects', function(source, args)
        local Player = QBCore.Functions.GetPlayer(source)
        if not Player then return end
        
        -- Kontrola admin práv
        if not QBCore.Functions.HasPermission(source, 'admin') then
            return
        end
        
        local effects = {
            health = tonumber(args[1]) or 0,
            armor = tonumber(args[2]) or 0,
            stress = tonumber(args[3]) or 0
        }
        
        ApplyEffects(source, effects, 'debug')
    end, false)
end
