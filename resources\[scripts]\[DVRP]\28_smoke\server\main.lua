-- Server script s podporou ox_inventory

-- Funkce pro registraci itemů v ox_inventory
local function RegisterOxInventoryItems()
    if GetResourceState('ox_inventory') == 'started' then
        -- Registrace cigaretových itemů
        if Config.Smoking.enabled then
            for _, item in pairs(Config.Smoking.items) do
                exports.ox_inventory:registerHook('swapItems', function(payload)
                    return false
                end, {
                    print = false,
                    itemFilter = {
                        [item.name] = true
                    }
                })
            end
        end

        -- Registrace snus itemů
        if Config.Snus.enabled then
            for _, item in pairs(Config.Snus.items) do
                exports.ox_inventory:registerHook('swapItems', function(payload)
                    return false
                end, {
                    print = false,
                    itemFilter = {
                        [item.name] = true
                    }
                })
            end
        end

        print("[28_smoke] Ox_inventory hooky registrovány")
    end
end

-- Exporty pro ox_inventory
exports('cigarette', function(event, item, inventory, slot, data)
    if event == 'usingItem' then
        TriggerClientEvent('28_smoke:useItem', inventory.id, 'cigarette')
        return
    end
end)

exports('doutnik', function(event, item, inventory, slot, data)
    if event == 'usingItem' then
        TriggerClientEvent('28_smoke:useItem', inventory.id, 'doutnik')
        return
    end
end)

exports('snus', function(event, item, inventory, slot, data)
    if event == 'usingItem' then
        TriggerClientEvent('28_smoke:useItem', inventory.id, 'snus')
        return
    end
end)

exports('snus_strong', function(event, item, inventory, slot, data)
    if event == 'usingItem' then
        TriggerClientEvent('28_smoke:useItem', inventory.id, 'snus_strong')
        return
    end
end)

-- Debug příkazy pro adminy
if Config.Debug then
    RegisterCommand('smoke_test', function(source, args)
        local playerId = source

        print(string.format("[28_smoke] Test příkaz spuštěn hráčem %d", playerId))

        -- Test notifikace
        TriggerClientEvent('chat:addMessage', playerId, {
            color = {255, 0, 0},
            multiline = true,
            args = {"[28_smoke]", "Test příkaz funguje!"}
        })
    end, false)

    RegisterCommand('smoke_armor', function(source, args)
        local playerId = source
        local amount = tonumber(args[1]) or 10

        local playerPed = GetPlayerPed(playerId)
        local currentArmor = GetPedArmour(playerPed)
        local newArmor = math.min(100, currentArmor + amount)
        SetPedArmour(playerPed, newArmor)

        TriggerClientEvent('chat:addMessage', playerId, {
            color = {0, 255, 0},
            multiline = true,
            args = {"[28_smoke]", string.format("Armor nastaven na %d", newArmor)}
        })

        print(string.format("[28_smoke] Hráč %d: Armor změněn na %d", playerId, newArmor))
    end, false)

    RegisterCommand('smoke_give', function(source, args)
        local playerId = source
        local itemName = args[1] or 'cigarette'
        local amount = tonumber(args[2]) or 1

        if GetResourceState('ox_inventory') == 'started' then
            exports.ox_inventory:AddItem(playerId, itemName, amount)
            TriggerClientEvent('chat:addMessage', playerId, {
                color = {0, 255, 0},
                multiline = true,
                args = {"[28_smoke]", string.format("Přidán item %s x%d", itemName, amount)}
            })
        end
    end, false)
end

-- Inicializace při startu
AddEventHandler('onResourceStart', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        Wait(1000)
        RegisterOxInventoryItems()

        print("[28_smoke] Script byl úspěšně načten!")
        print("[28_smoke] Podporované itemy:")

        if Config.Smoking.enabled then
            for _, item in pairs(Config.Smoking.items) do
                print(string.format("  - %s (%s) - +%d armor", item.name, item.label, item.armor))
            end
        end

        if Config.Snus.enabled then
            for _, item in pairs(Config.Snus.items) do
                print(string.format("  - %s (%s) - +%d armor", item.name, item.label, item.armor))
            end
        end

        if Config.Debug then
            print("[28_smoke] Debug příkazy:")
            print("  /smoke_test - Test příkaz")
            print("  /smoke_armor [amount] - Přidá armor")
            print("  /smoke_give [item] [amount] - Přidá item")
        end
    end
end)
