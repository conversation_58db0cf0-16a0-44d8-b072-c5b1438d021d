# 28_smoke - Plně Automatický Smoking & Snus System

## Popis
Kompletně automatický systém pro kouření cigaret a používání snusu s ox_inventory. Script sám registruje itemy a hooky - ŽÁDNÉ EXPORTY ani ruční úpravy!

## Funkce
- ✅ Kouření cigaret s animací a armor efekty
- ✅ Používání snusu s animací a armor efekty
- ✅ Konfigurovatelné animace pro každý item
- ✅ Pouze armor efekty (jednoduché a efektivní)
- ✅ Standalone progress bar a notifikace
- ✅ Prop objekty během animace
- ✅ Podpora pro více typů cigaret a snusu
- ✅ **AUTOMATICKÁ REGISTRACE** - script sám přidá itemy do ox_inventory
- ✅ **AUTOMATICKÉ HOOKY** - script sám zachytí použití itemů
- ✅ **ŽÁDNÉ EXPORTY** - vše je interní
- ✅ Automatické odebrání itemů z inventáře
- ✅ Debug režim pro testování
- ✅ Fallback příkazy

## Instalace

1. Zkopírujte složku `28_smoke` do `resources/[scripts]/[DVRP]/`
2. Přidejte do `server.cfg`:
   ```
   ensure 28_smoke
   ```
3. Restartujte server
4. **HOTOVO!** - Vše je automatické!

## Závislosti
- **ox_inventory** (povinné)
- **ŽÁDNÝ FRAMEWORK** - kompletně standalone!
- **ŽÁDNÉ EXPORTY** - vše interní!
- **ŽÁDNÉ RUČNÍ ÚPRAVY** - 100% automatické!

## 🚀 PLNĚ AUTOMATICKÉ NASTAVENÍ

### ✅ ŽÁDNÉ EXPORTY! ŽÁDNÉ RUČNÍ ÚPRAVY!

Script automaticky:
1. **Registruje hooky** pro zachycení použití itemů
2. **Spravuje odebrání** itemů z inventáře
3. **Vše bez exportů** - používá pouze hooky
4. **Žádné ruční úpravy** ox_inventory souborů
5. **Kompletně standalone** - žádný framework

### Podporované itemy (musí být v ox_inventory):
- **cigarette** - Cigareta (+10 armor)
- **doutnik** - Doutník (+15 armor)
- **snus** - Snus (+5 armor)
- **snus_strong** - Silný snus (+8 armor)

### Použití itemů:
1. **Přidejte itemy do ox_inventory** (viz níže)
2. **Získejte item** (pomocí debug příkazu nebo jiného scriptu)
3. **Klikněte pravým tlačítkem** na item v inventáři
4. **Vyberte "Použít"**
5. **Script automaticky zachytí** použití přes hook
6. **Item se automaticky odebere** a použije s animací
7. **Získáte armor** podle typu itemu

### Fallback příkazy (pokud inventář nefunguje):
- `/use_cigarette` - Použije základní cigaretu (+10 armor)
- `/use_doutnik` - Použije doutník (+15 armor)
- `/use_snus` - Použije základní snus (+5 armor)
- `/use_snus_strong` - Použije silný snus (+8 armor)

### Debug příkazy (pouze když je Config.Debug = true):
- `/smoke_test` - Test příkaz
- `/smoke_armor [amount]` - Přidá armor
- `/smoke_give [item] [amount]` - Přidá item do inventáře

## Přidání itemů do ox_inventory

**DŮLEŽITÉ:** Musíte ručně přidat itemy do `ox_inventory/data/items.lua`:

```lua
['cigarette'] = {
    label = 'Cigareta',
    weight = 10,
    close = true,
    consume = 0
},

['doutnik'] = {
    label = 'Doutník',
    weight = 15,
    close = true,
    consume = 0
},

['snus'] = {
    label = 'Snus',
    weight = 5,
    close = true,
    consume = 0
},

['snus_strong'] = {
    label = 'Silný snus',
    weight = 8,
    close = true,
    consume = 0
}
```

**Poznámka:** `consume = 0` je důležité - script si sám spravuje odebrání itemů!

## Jak to funguje

### Automatická registrace itemů
Script při startu automaticky registruje všechny itemy:

```lua
-- V server/main.lua:
local function RegisterItemsToOxInventory()
    local itemsToRegister = {}
    
    -- Přidáme cigaretové itemy z configu
    for _, item in pairs(Config.Smoking.items) do
        itemsToRegister[item.name] = {
            label = item.label,
            weight = 10,
            close = true,
            consume = 0, -- Nebudeme automaticky odebírat, uděláme to sami
            client = {
                usetime = item.duration,
                cancel = true,
                disable = { move = true, car = true, combat = true }
            }
        }
    end
    
    -- Registrujeme všechny itemy najednou
    for itemName, itemData in pairs(itemsToRegister) do
        exports.ox_inventory:registerItem(itemName, itemData)
    end
end
```

### Automatické hooky pro použití itemů
```lua
-- Hook pro zachycení použití itemů z inventáře
exports.ox_inventory:registerHook('swapItems', function(payload)
    local fromSlot = payload.fromSlot
    local toSlot = payload.toSlot
    
    -- Kontrolujeme, jestli se item používá (pravý klik -> použít)
    if fromSlot and fromSlot.name and toInventory and string.find(toInventory, 'use') then
        local itemName = fromSlot.name
        local playerId = fromInventory
        
        if isOurItem then
            UseItem(playerId, itemName)
            return false -- Zabráníme normálnímu přesunu
        end
    end
    
    return true -- Povolíme normální přesun pro ostatní itemy
end)
```

### Výhody tohoto přístupu:
- ✅ **Žádné exporty** - vše je interní
- ✅ **Automatické odebrání** - script se postará o odebrání
- ✅ **Standalone** - žádný framework potřeba
- ✅ **Jednoduché** - stačí spustit script
- ✅ **Flexibilní** - snadno přidávat nové itemy v config.lua
- ✅ **Bezpečné** - žádné konflikty s jinými scripty

## Konfigurace

### Základní nastavení
V `config.lua` můžete upravit:
- `Config.Debug` - zapne debug režim
- `Config.UseProgressBar` - použití progress baru

### Současné itemy v konfiguraci:
- **cigarette** - Základní cigareta (+10 armor)
- **doutnik** - Doutník (+15 armor) 
- **snus** - Základní snus (+5 armor)
- **snus_strong** - Silný snus (+8 armor)

## Přidání nových itemů

### 1. Přidejte do config.lua
```lua
-- Pro cigaretu
{
    name = "cigarette_luxury",
    label = "Luxusní cigareta",
    armor = 20,
    animation = "cigarette",
    duration = 15000,
    prop = {
        model = "prop_cs_ciggy_01",
        bone = 47419,
        coords = vector3(0.015, -0.009, 0.003),
        rotation = vector3(55.0, 0.0, 110.0)
    }
}
```

### 2. Restartujte script
Script automaticky zaregistruje nový item - žádné další kroky nejsou potřeba!

## Troubleshooting

### Itemy nefungují:
1. Zkontrolujte, že je ox_inventory spuštěn
2. Zkontrolujte console pro chyby při startu scriptu
3. Použijte debug příkazy pro testování

### Debug:
- Nastavte `Config.Debug = true` v config.lua
- Restartujte script
- Sledujte console pro debug zprávy:
  ```
  [28_smoke] Registrován item: cigarette (Cigareta)
  [28_smoke] Registrován item: doutnik (doutnik)
  [28_smoke] Registrováno 4 itemů do ox_inventory
  [28_smoke] Ox_inventory hooky registrovány
  ```
- Použijte `/smoke_test` pro test funkčnosti

### Časté problémy:
- **Item se neodebere z inventáře**: Script používá vlastní odebrání přes hooky
- **Animace nefunguje**: Zkontrolujte console pro chyby s animacemi
- **Armor se nepřidá**: Zkontrolujte, že není armor již na maximu (100)

## Podpora
Pro podporu kontaktujte šlechy (autor scriptu).

## Changelog
- v1.3.0 - Plně automatický systém bez exportů (pouze hooky)
- v1.2.0 - QBCore useable items systém
- v1.1.0 - Přidána podpora ox_inventory exportů
- v1.0.0 - Standalone verze s pouze armor efekty
