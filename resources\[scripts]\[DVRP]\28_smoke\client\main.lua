local isUsing = false
local currentProp = nil
local currentAnimation = nil

-- Funkce pro načtení animace
local function LoadAnimDict(dict)
    if not HasAnimDictLoaded(dict) then
        RequestAnimDict(dict)
        while not HasAnimDictLoaded(dict) do
            Wait(1)
        end
    end
end

-- Funkce pro vytvoření propu
local function CreateProp(propData)
    local playerPed = PlayerPedId()
    local coords = GetEntityCoords(playerPed)

    RequestModel(propData.model)
    while not HasModelLoaded(propData.model) do
        Wait(1)
    end

    local prop = CreateObject(GetHashKey(propData.model), coords.x, coords.y, coords.z, true, true, true)
    AttachEntityToEntity(prop, playerPed, GetPedBoneIndex(playerPed, propData.bone),
                        propData.coords.x, propData.coords.y, propData.coords.z,
                        propData.rotation.x, propData.rotation.y, propData.rotation.z,
                        true, true, false, true, 1, true)

    return prop
end

-- Funkce pro smazání propu
local function DeleteProp()
    if currentProp then
        DeleteObject(currentProp)
        currentProp = nil
    end
end

-- Funkce pro zastavení animace
local function StopAnimation()
    local playerPed = PlayerPedId()
    ClearPedTasks(playerPed)
    DeleteProp()
    currentAnimation = nil
end

-- Inicializace frameworku
local Framework = nil
local FrameworkName = Config.Framework

if FrameworkName == "qbox" then
    Framework = exports['qbx_core']:GetCoreObject()
elseif FrameworkName == "esx" then
    Framework = exports['es_extended']:getSharedObject()
end

-- Funkce pro notifikaci podle frameworku
local function ShowNotification(message, type)
    type = type or "primary"

    if FrameworkName == "qbox" then
        TriggerEvent('QBCore:Notify', message, type)
    elseif FrameworkName == "esx" then
        TriggerEvent('esx:showNotification', message)
    else
        -- Fallback standalone notifikace
        SetNotificationTextEntry("STRING")
        AddTextComponentString(message)
        DrawNotification(false, false)
    end
end

-- Funkce pro progress bar (standalone)
local function ShowProgressBar(label, duration, callback)
    if Config.UseProgressBar then
        -- Jednoduchý progress bar bez závislostí
        local startTime = GetGameTimer()
        local endTime = startTime + duration

        CreateThread(function()
            while GetGameTimer() < endTime do
                local remaining = endTime - GetGameTimer()
                local progress = math.floor((duration - remaining) / duration * 100)

                -- Zobrazení progress textu
                SetTextFont(4)
                SetTextProportional(1)
                SetTextScale(0.0, 0.5)
                SetTextColour(255, 255, 255, 255)
                SetTextDropshadow(0, 0, 0, 0, 255)
                SetTextEdge(1, 0, 0, 0, 255)
                SetTextDropShadow()
                SetTextOutline()
                SetTextEntry("STRING")
                AddTextComponentString(label .. " (" .. progress .. "%)")
                DrawText(0.5, 0.85)

                -- Kontrola zrušení (ESC klávesa)
                if IsControlJustPressed(0, 322) then -- ESC
                    callback(false)
                    return
                end

                Wait(100)
            end
            callback(true)
        end)
    else
        Wait(duration)
        callback(true)
    end
end

-- Hlavní funkce pro kouření
local function StartSmoking(itemData)
    if isUsing then
        ShowNotification(Config.Notifications.already_using)
        return
    end

    isUsing = true
    local playerPed = PlayerPedId()

    -- Načtení animace
    local animData = Config.Smoking.animations[itemData.animation]
    LoadAnimDict(animData.dict)

    -- Vytvoření propu
    if itemData.prop then
        currentProp = CreateProp(itemData.prop)
    end

    -- Spuštění animace
    TaskPlayAnim(playerPed, animData.dict, animData.anim, 8.0, -8.0, -1, animData.flag, 0, false, false, false)
    currentAnimation = animData

    ShowNotification(Config.Notifications.smoking_started)

    -- Progress bar
    ShowProgressBar("Kouříte...", itemData.duration, function(completed)
        if completed then
            -- Aplikace armor efektu
            local currentArmor = GetPedArmour(playerPed)
            local newArmor = math.min(100, currentArmor + itemData.armor)
            SetPedArmour(playerPed, newArmor)

            ShowNotification(Config.Notifications.smoking_finished)
        else
            ShowNotification(Config.Notifications.cancelled)
        end

        StopAnimation()
        isUsing = false
    end)
end

-- Hlavní funkce pro snus
local function StartSnus(itemData)
    if isUsing then
        ShowNotification(Config.Notifications.already_using)
        return
    end

    isUsing = true
    local playerPed = PlayerPedId()

    -- Načtení animace
    local animData = Config.Snus.animations[itemData.animation]
    LoadAnimDict(animData.dict)

    -- Vytvoření propu
    if itemData.prop then
        currentProp = CreateProp(itemData.prop)
    end

    -- Spuštění animace
    TaskPlayAnim(playerPed, animData.dict, animData.anim, 8.0, -8.0, -1, animData.flag, 0, false, false, false)
    currentAnimation = animData

    ShowNotification(Config.Notifications.snus_started)

    -- Progress bar
    ShowProgressBar("Používáte snus...", itemData.duration, function(completed)
        if completed then
            -- Aplikace armor efektu
            local currentArmor = GetPedArmour(playerPed)
            local newArmor = math.min(100, currentArmor + itemData.armor)
            SetPedArmour(playerPed, newArmor)

            ShowNotification(Config.Notifications.snus_finished)
        else
            ShowNotification(Config.Notifications.cancelled)
        end

        StopAnimation()
        isUsing = false
    end)
end

-- Event pro použití itemů z ox_inventory
RegisterNetEvent('28_smoke:useItem', function(itemName)
    -- Najdeme item v konfiguraci
    local itemData = nil
    local itemType = nil

    -- Hledáme v cigaretách
    if Config.Smoking.enabled then
        for _, item in pairs(Config.Smoking.items) do
            if item.name == itemName then
                itemData = item
                itemType = 'smoking'
                break
            end
        end
    end

    -- Hledáme v snusu
    if not itemData and Config.Snus.enabled then
        for _, item in pairs(Config.Snus.items) do
            if item.name == itemName then
                itemData = item
                itemType = 'snus'
                break
            end
        end
    end

    -- Použijeme item
    if itemData then
        if itemType == 'smoking' then
            StartSmoking(itemData)
        elseif itemType == 'snus' then
            StartSnus(itemData)
        end
    else
        ShowNotification("Neznámý item: " .. itemName)
    end
end)

-- Registrace příkazů pro použití itemů (fallback)
RegisterCommand('use_cigarette', function()
    if not Config.Smoking.enabled then return end

    local item = Config.Smoking.items[1]
    if item then
        StartSmoking(item)
    end
end, false)

RegisterCommand('use_doutnik', function()
    if not Config.Smoking.enabled then return end

    for _, item in pairs(Config.Smoking.items) do
        if item.name == "doutnik" then
            StartSmoking(item)
            break
        end
    end
end, false)

RegisterCommand('use_snus', function()
    if not Config.Snus.enabled then return end

    local item = Config.Snus.items[1]
    if item then
        StartSnus(item)
    end
end, false)

RegisterCommand('use_snus_strong', function()
    if not Config.Snus.enabled then return end

    for _, item in pairs(Config.Snus.items) do
        if item.name == "snus_strong" then
            StartSnus(item)
            break
        end
    end
end, false)

-- Zastavení při smrti nebo odpojení
AddEventHandler('onResourceStop', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        StopAnimation()
        isUsing = false
    end
end)

CreateThread(function()
    while true do
        Wait(1000)

        if isUsing and IsPedDeadOrDying(PlayerPedId()) then
            StopAnimation()
            isUsing = false
        end
    end
end)
