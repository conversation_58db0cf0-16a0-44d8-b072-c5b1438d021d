local QBCore = exports['qb-core']:GetCoreObject()
local isUsing = false
local currentProp = nil
local currentAnimation = nil

-- Funkce pro načtení animace
local function LoadAnimDict(dict)
    if not HasAnimDictLoaded(dict) then
        RequestAnimDict(dict)
        while not HasAnimDictLoaded(dict) do
            Wait(1)
        end
    end
end

-- Funkce pro vytvoření propu
local function CreateProp(propData)
    local playerPed = PlayerPedId()
    local coords = GetEntityCoords(playerPed)
    
    RequestModel(propData.model)
    while not HasModelLoaded(propData.model) do
        Wait(1)
    end
    
    local prop = CreateObject(GetHashKey(propData.model), coords.x, coords.y, coords.z, true, true, true)
    AttachEntityToEntity(prop, playerPed, GetPedBoneIndex(playerPed, propData.bone), 
                        propData.coords.x, propData.coords.y, propData.coords.z,
                        propData.rotation.x, propData.rotation.y, propData.rotation.z,
                        true, true, false, true, 1, true)
    
    return prop
end

-- Funkce pro smazání propu
local function DeleteProp()
    if currentProp then
        DeleteObject(currentProp)
        currentProp = nil
    end
end

-- Funkce pro zastavení animace
local function StopAnimation()
    local playerPed = PlayerPedId()
    ClearPedTasks(playerPed)
    DeleteProp()
    currentAnimation = nil
end

-- Funkce pro notifikaci
local function ShowNotification(message)
    if Config.Framework == "qb" then
        QBCore.Functions.Notify(message, "primary")
    else
        -- ESX notifikace
        TriggerEvent('esx:showNotification', message)
    end
end

-- Funkce pro progress bar
local function ShowProgressBar(label, duration, callback)
    if Config.UseProgressBar then
        if exports['ox_lib'] then
            if exports['ox_lib']:progressBar({
                duration = duration,
                label = label,
                useWhileDead = false,
                canCancel = true,
                disable = {
                    car = true,
                    move = true,
                    combat = true
                }
            }) then
                callback(true)
            else
                callback(false)
            end
        else
            -- Fallback bez progress baru
            Wait(duration)
            callback(true)
        end
    else
        Wait(duration)
        callback(true)
    end
end

-- Hlavní funkce pro kouření
local function StartSmoking(itemData)
    if isUsing then
        ShowNotification(Config.Notifications.already_using)
        return
    end
    
    isUsing = true
    local playerPed = PlayerPedId()
    
    -- Načtení animace
    local animData = Config.Smoking.animations[itemData.animation]
    LoadAnimDict(animData.dict)
    
    -- Vytvoření propu
    if itemData.prop then
        currentProp = CreateProp(itemData.prop)
    end
    
    -- Spuštění animace
    TaskPlayAnim(playerPed, animData.dict, animData.anim, 8.0, -8.0, -1, animData.flag, 0, false, false, false)
    currentAnimation = animData
    
    ShowNotification(Config.Notifications.smoking_started)
    
    -- Progress bar
    ShowProgressBar("Kouříte...", itemData.duration, function(completed)
        if completed then
            -- Aplikace efektů
            TriggerServerEvent('28_smoke:applyEffects', itemData.effects, 'smoking')
            ShowNotification(Config.Notifications.smoking_finished)
        else
            ShowNotification(Config.Notifications.cancelled)
        end
        
        StopAnimation()
        isUsing = false
    end)
end

-- Hlavní funkce pro snus
local function StartSnus(itemData)
    if isUsing then
        ShowNotification(Config.Notifications.already_using)
        return
    end
    
    isUsing = true
    local playerPed = PlayerPedId()
    
    -- Načtení animace
    local animData = Config.Snus.animations[itemData.animation]
    LoadAnimDict(animData.dict)
    
    -- Vytvoření propu
    if itemData.prop then
        currentProp = CreateProp(itemData.prop)
    end
    
    -- Spuštění animace
    TaskPlayAnim(playerPed, animData.dict, animData.anim, 8.0, -8.0, -1, animData.flag, 0, false, false, false)
    currentAnimation = animData
    
    ShowNotification(Config.Notifications.snus_started)
    
    -- Progress bar
    ShowProgressBar("Používáte snus...", itemData.duration, function(completed)
        if completed then
            -- Aplikace efektů
            TriggerServerEvent('28_smoke:applyEffects', itemData.effects, 'snus')
            ShowNotification(Config.Notifications.snus_finished)
        else
            ShowNotification(Config.Notifications.cancelled)
        end
        
        StopAnimation()
        isUsing = false
    end)
end

-- Event handlery pro použití itemů
RegisterNetEvent('28_smoke:useCigarette', function(itemName)
    if not Config.Smoking.enabled then return end
    
    for _, item in pairs(Config.Smoking.items) do
        if item.name == itemName then
            StartSmoking(item)
            break
        end
    end
end)

RegisterNetEvent('28_smoke:useSnus', function(itemName)
    if not Config.Snus.enabled then return end
    
    for _, item in pairs(Config.Snus.items) do
        if item.name == itemName then
            StartSnus(item)
            break
        end
    end
end)

-- Zastavení při smrti nebo odpojení
AddEventHandler('onResourceStop', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        StopAnimation()
        isUsing = false
    end
end)

CreateThread(function()
    while true do
        Wait(1000)
        
        if isUsing and IsPedDeadOrDying(PlayerPedId()) then
            StopAnimation()
            isUsing = false
        end
    end
end)
