Config = {}

-- <PERSON>áklad<PERSON><PERSON> nastavení
Config.Debug = false
Config.UseOxTarget = true -- Pokud chcete použít ox_target pro interakce
Config.UseProgressBar = true -- Pokud chcete použít progress bar při kouřen<PERSON>/snusování

-- Kouření cigaret
Config.Smoking = {
    enabled = true,
    
    -- Animace pro kouření
    animations = {
        cigarette = {
            dict = "amb@world_human_smoking@male@male_a@enter",
            anim = "enter",
            flag = 49,
            duration = 10000 -- 10 sekund
        },
        cigarette_idle = {
            dict = "amb@world_human_smoking@male@male_a@idle_a",
            anim = "idle_a",
            flag = 49
        }
    },
    
    -- Efekty kouření
    effects = {
        health = -5,        -- Ztr<PERSON>ta zdraví
        armor = 10,         -- Přidání armoru
        stress = -20,       -- Sn<PERSON><PERSON><PERSON><PERSON> stresu (pokud máte stress systém)
        thirst = -5,        -- <PERSON><PERSON><PERSON><PERSON>ě
        hunger = -3         -- <PERSON><PERSON><PERSON><PERSON> hlad<PERSON>
    },
    
    -- Itemy pro kouření
    items = {
        {
            name = "cigarette",
            label = "Cigareta",
            effects = {
                health = -5,
                armor = 10,
                stress = -20,
                thirst = -5,
                hunger = -3
            },
            animation = "cigarette",
            duration = 10000,
            prop = {
                model = "prop_cs_ciggy_01",
                bone = 47419,
                coords = vector3(0.015, -0.009, 0.003),
                rotation = vector3(55.0, 0.0, 110.0)
            }
        },
        {
            name = "cigarette_premium",
            label = "Prémiová cigareta",
            effects = {
                health = -3,
                armor = 15,
                stress = -30,
                thirst = -3,
                hunger = -2
            },
            animation = "cigarette",
            duration = 12000,
            prop = {
                model = "prop_cs_ciggy_01",
                bone = 47419,
                coords = vector3(0.015, -0.009, 0.003),
                rotation = vector3(55.0, 0.0, 110.0)
            }
        }
    }
}

-- Snus systém
Config.Snus = {
    enabled = true,
    
    -- Animace pro snus
    animations = {
        snus_use = {
            dict = "mp_player_intdrink",
            anim = "loop_bottle",
            flag = 49,
            duration = 3000 -- 3 sekundy
        },
        snus_idle = {
            dict = "amb@world_human_drinking@coffee@male@idle_a",
            anim = "idle_a",
            flag = 49
        }
    },
    
    -- Efekty snusu
    effects = {
        health = 5,         -- Přidání zdraví
        armor = 5,          -- Přidání armoru
        stress = -15,       -- Snížení stresu
        stamina = 20        -- Přidání staminy
    },
    
    -- Itemy pro snus
    items = {
        {
            name = "snus",
            label = "Snus",
            effects = {
                health = 5,
                armor = 5,
                stress = -15,
                stamina = 20
            },
            animation = "snus_use",
            duration = 3000,
            prop = {
                model = "prop_food_bs_chips",
                bone = 18905,
                coords = vector3(0.13, 0.05, 0.02),
                rotation = vector3(-50.0, 16.0, 60.0)
            }
        },
        {
            name = "snus_strong",
            label = "Silný snus",
            effects = {
                health = 8,
                armor = 8,
                stress = -25,
                stamina = 30
            },
            animation = "snus_use",
            duration = 4000,
            prop = {
                model = "prop_food_bs_chips",
                bone = 18905,
                coords = vector3(0.13, 0.05, 0.02),
                rotation = vector3(-50.0, 16.0, 60.0)
            }
        }
    }
}

-- Notifikace
Config.Notifications = {
    smoking_started = "Začínáte kouřit...",
    smoking_finished = "Dokončili jste kouření",
    snus_started = "Používáte snus...",
    snus_finished = "Dokončili jste používání snusu",
    no_item = "Nemáte potřebný item",
    already_using = "Již něco používáte",
    cancelled = "Akce byla zrušena"
}

-- Framework nastavení
Config.Framework = "qb" -- "qb" nebo "esx"
