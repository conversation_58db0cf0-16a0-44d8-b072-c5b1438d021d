# 28_smoke - Smoking & Snus System s QBox/ESX podporou

## Popis
Systém pro kouření cigaret a používání snusu s podporou QBox a ESX frameworků. Automatické hooky pro ox_inventory nebo framework inventář.

## Funkce
- ✅ Kouření cigaret s animací a armor efekty
- ✅ Používání snusu s animací a armor efekty
- ✅ Konfigurovatelné animace pro každý item
- ✅ Pouze armor efekty (jednoduché a efektivní)
- ✅ Progress bar a notifikace podle frameworku
- ✅ Prop objekty během animace
- ✅ Podpora pro více typů cigaret a snusu
- ✅ **PODPORA FRAMEWORKŮ** - QBox a ESX
- ✅ **AUTOMATICKÉ HOOKY** - ox_inventory nebo framework inventář
- ✅ **ŽÁDNÉ EXPORTY** - vše je interní
- ✅ Automatické odebrání itemů z inventáře
- ✅ Debug režim pro testování
- ✅ Fallback příkazy

## Instalace

1. Zkopírujte složku `28_smoke` do `resources/[scripts]/[DVRP]/`
2. Nastavte framework v `config.lua`:
   ```lua
   Config.Framework = "qbox" -- nebo "esx"
   ```
3. Přidejte do `server.cfg`:
   ```
   ensure 28_smoke
   ```
4. Přidejte itemy do inventáře (viz níže)
5. Restartujte server

## Závislosti
- **QBox** nebo **ESX** framework
- **ox_inventory** (doporučeno) nebo framework inventář

## 🚀 KONFIGURACE FRAMEWORKU

### Nastavení v config.lua:
```lua
Config.Framework = "qbox" -- nebo "esx"
```

### QBox Framework:
- Používá `qbx_core` export
- QBCore notifikace
- QBox inventář nebo ox_inventory

### ESX Framework:
- Používá `es_extended` export
- ESX notifikace
- ESX inventář nebo ox_inventory

## Podporované itemy:
- **cigarette** - Cigareta (+10 armor)
- **doutnik** - Doutník (+15 armor)
- **snus** - Snus (+5 armor)
- **snus_strong** - Silný snus (+8 armor)

## Použití itemů:
1. **Přidejte itemy do inventáře** (viz níže)
2. **Získejte item** (pomocí debug příkazu nebo jiného scriptu)
3. **Klikněte pravým tlačítkem** na item v inventáři
4. **Vyberte "Použít"**
5. **Script automaticky zachytí** použití přes hook
6. **Item se automaticky odebere** a použije s animací
7. **Získáte armor** podle typu itemu

### Fallback příkazy:
- `/use_cigarette` - Použije základní cigaretu (+10 armor)
- `/use_doutnik` - Použije doutník (+15 armor)
- `/use_snus` - Použije základní snus (+5 armor)
- `/use_snus_strong` - Použije silný snus (+8 armor)

### Debug příkazy (pouze když je Config.Debug = true):
- `/smoke_test` - Test příkaz
- `/smoke_armor [amount]` - Přidá armor
- `/smoke_give [item] [amount]` - Přidá item do inventáře

## Přidání itemů do inventáře

### Pro ox_inventory:
Přidejte do `ox_inventory/data/items.lua`:

```lua
['cigarette'] = {
    label = 'Cigareta',
    weight = 10,
    close = true,
    consume = 0  -- Důležité! Script si sám spravuje odebrání
},

['doutnik'] = {
    label = 'Doutník',
    weight = 15,
    close = true,
    consume = 0
},

['snus'] = {
    label = 'Snus',
    weight = 5,
    close = true,
    consume = 0
},

['snus_strong'] = {
    label = 'Silný snus',
    weight = 8,
    close = true,
    consume = 0
}
```

### Pro QBox framework:
Přidejte do `qbx_core/shared/items.lua`:

```lua
cigarette = {
    name = 'cigarette',
    label = 'Cigareta',
    weight = 10,
    type = 'item',
    image = 'cigarette.png',
    unique = false,
    useable = true,
    shouldClose = true,
    description = 'Klasická cigareta pro kouření'
},

doutnik = {
    name = 'doutnik',
    label = 'Doutník',
    weight = 15,
    type = 'item',
    image = 'doutnik.png',
    unique = false,
    useable = true,
    shouldClose = true,
    description = 'Kvalitní doutník'
},

snus = {
    name = 'snus',
    label = 'Snus',
    weight = 5,
    type = 'item',
    image = 'snus.png',
    unique = false,
    useable = true,
    shouldClose = true,
    description = 'Švédský snus'
},

snus_strong = {
    name = 'snus_strong',
    label = 'Silný snus',
    weight = 8,
    type = 'item',
    image = 'snus_strong.png',
    unique = false,
    useable = true,
    shouldClose = true,
    description = 'Silný snus s intenzivními efekty'
}
```

### Pro ESX framework:
Přidejte do databáze `items` tabulky:

```sql
INSERT INTO `items` (`name`, `label`, `weight`, `rare`, `can_remove`) VALUES
('cigarette', 'Cigareta', 10, 0, 1),
('doutnik', 'Doutník', 15, 0, 1),
('snus', 'Snus', 5, 0, 1),
('snus_strong', 'Silný snus', 8, 0, 1);
```

## Jak to funguje

### Automatické hooky pro použití itemů
Script používá ox_inventory hooky nebo framework eventy:

```lua
-- Pro ox_inventory
exports.ox_inventory:registerHook('swapItems', function(payload)
    if isOurItem then
        UseItem(playerId, itemName, itemData)
        return false -- Zabráníme normálnímu přesunu
    end
    return true
end)
```

### Výhody tohoto přístupu:
- ✅ **Podpora frameworků** - QBox a ESX
- ✅ **Žádné exporty** - vše je interní
- ✅ **Automatické odebrání** - script se postará o odebrání
- ✅ **Flexibilní** - ox_inventory nebo framework inventář
- ✅ **Jednoduché** - stačí nastavit framework v configu

## Konfigurace

### Základní nastavení
V `config.lua` můžete upravit:
- `Config.Framework` - "qbox" nebo "esx"
- `Config.Debug` - zapne debug režim
- `Config.UseProgressBar` - použití progress baru

### Současné itemy v konfiguraci:
- **cigarette** - Základní cigareta (+10 armor)
- **doutnik** - Doutník (+15 armor) 
- **snus** - Základní snus (+5 armor)
- **snus_strong** - Silný snus (+8 armor)

## Přidání nových itemů

### 1. Přidejte do config.lua
```lua
-- Pro cigaretu
{
    name = "cigarette_luxury",
    label = "Luxusní cigareta",
    armor = 20,
    animation = "cigarette",
    duration = 15000,
    prop = {
        model = "prop_cs_ciggy_01",
        bone = 47419,
        coords = vector3(0.015, -0.009, 0.003),
        rotation = vector3(55.0, 0.0, 110.0)
    }
}
```

### 2. Přidejte do inventáře
Podle vašeho frameworku přidejte item do příslušného inventáře (viz výše).

### 3. Restartujte script
Script automaticky rozpozná nový item - žádné další kroky nejsou potřeba!

## Troubleshooting

### Itemy nefungují:
1. Zkontrolujte, že je správný framework spuštěn
2. Zkontrolujte nastavení `Config.Framework` v config.lua
3. Zkontrolujte, že jste přidali itemy do inventáře
4. Zkontrolujte console pro chyby při startu scriptu

### Debug:
- Nastavte `Config.Debug = true` v config.lua
- Restartujte script
- Sledujte console pro debug zprávy
- Použijte `/smoke_test` pro test funkčnosti

## Podpora
Pro podporu kontaktujte šlechy (autor scriptu).

## Changelog
- v2.0.0 - Přidána podpora QBox a ESX frameworků
- v1.3.0 - Plně automatický systém bez exportů (pouze hooky)
- v1.2.0 - QBCore useable items systém
- v1.1.0 - Přidána podpora ox_inventory exportů
- v1.0.0 - Standalone verze s pouze armor efekty
