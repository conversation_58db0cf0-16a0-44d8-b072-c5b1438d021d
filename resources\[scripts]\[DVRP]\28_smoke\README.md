# 28_smoke - Standalone Smoking & Snus System pro Ox_Inventory

## Popis
Kompletně standalone systém pro kouření cigaret a používání snusu pouze s ox_inventory, bez exportů a bez frameworku.

## Funkce
- ✅ Kouření cigaret s animací a armor efekty
- ✅ Používání snusu s animací a armor efekty
- ✅ Konfigurovatelné animace pro každý item
- ✅ Pouze armor efekty (jednoduché a efektivní)
- ✅ Standalone progress bar a notifikace
- ✅ Prop objekty během animace
- ✅ Podpora pro více typů cigaret a snusu
- ✅ **ŽÁDNÉ EXPORTY** - automatická registrace v ox_inventory
- ✅ Automatické odebrání itemů z inventáře
- ✅ Debug režim pro testování
- ✅ Fallback příkazy

## Instalace

1. Zkopírujte složku `28_smoke` do `resources/[scripts]/[DVRP]/`
2. Přidejte do `server.cfg`:
   ```
   ensure 28_smoke
   ```
3. Přidejte itemy do ox_inventory (viz níže)
4. Restartujte server

## Závislosti
- **ox_inventory** (povinné)
- **ŽÁDNÝ FRAMEWORK** - kompletně standalone!

## Ox_Inventory Setup

### 1. Přidání itemů do ox_inventory

Přidejte do `ox_inventory/data/items.lua`:

```lua
['cigarette'] = {
    label = 'Cigareta',
    weight = 10,
    close = true,
    consume = 1,
    client = {
        usetime = 10000,
        cancel = true,
        disable = {
            move = true,
            car = true,
            combat = true
        }
    },
    server = {
        export = '28_smoke.useItem'
    }
},

['doutnik'] = {
    label = 'Doutník',
    weight = 15,
    close = true,
    consume = 1,
    client = {
        usetime = 12000,
        cancel = true,
        disable = {
            move = true,
            car = true,
            combat = true
        }
    },
    server = {
        export = '28_smoke.useItem'
    }
},

['snus'] = {
    label = 'Snus',
    weight = 5,
    close = true,
    consume = 1,
    client = {
        usetime = 3000,
        cancel = true,
        disable = {
            move = true,
            car = true,
            combat = true
        }
    },
    server = {
        export = '28_smoke.useItem'
    }
},

['snus_strong'] = {
    label = 'Silný snus',
    weight = 8,
    close = true,
    consume = 1,
    client = {
        usetime = 4000,
        cancel = true,
        disable = {
            move = true,
            car = true,
            combat = true
        }
    },
    server = {
        export = '28_smoke.useItem'
    }
}
```

### 2. Použití itemů
- Klikněte pravým tlačítkem na item v inventáři
- Vyberte "Použít"
- Item se automaticky odebere a použije s animací
- Získáte armor podle typu itemu

### 3. Fallback příkazy
- `/use_cigarette` - Použije základní cigaretu (+10 armor)
- `/use_doutnik` - Použije doutník (+15 armor)
- `/use_snus` - Použije základní snus (+5 armor)
- `/use_snus_strong` - Použije silný snus (+8 armor)

### 4. Debug příkazy (pouze když je Config.Debug = true)
- `/smoke_test` - Test příkaz
- `/smoke_armor [amount]` - Přidá armor
- `/smoke_give [item] [amount]` - Přidá item do inventáře

## Jak to funguje

### Ox_Inventory Export System
Script používá pouze jeden univerzální export pro všechny itemy:

```lua
-- V server/main.lua:
exports('useItem', function(event, item, inventory, slot, data)
    if event == 'usingItem' then
        local playerId = inventory.id
        local itemName = item.name

        -- Spustíme použití itemu
        TriggerClientEvent('28_smoke:useItem', playerId, itemName)

        return true
    end
end)
```

### Výhody tohoto přístupu:
- ✅ **Pouze jeden export** - `28_smoke.useItem` pro všechny itemy
- ✅ **Automatické odebrání** - ox_inventory se postará o odebrání
- ✅ **Standalone** - žádný framework potřeba
- ✅ **Jednoduché** - stačí přidat itemy do ox_inventory
- ✅ **Stabilní** - používá standardní ox_inventory systém

## Konfigurace

### Základní nastavení
V `config.lua` můžete upravit:
- `Config.Debug` - zapne debug režim
- `Config.UseProgressBar` - použití progress baru

### Současné itemy v konfiguraci:
- **cigarette** - Základní cigareta (+10 armor)
- **doutnik** - Doutník (+15 armor)
- **snus** - Základní snus (+5 armor)
- **snus_strong** - Silný snus (+8 armor)

## Přidání nových itemů

### 1. Přidejte do config.lua
```lua
-- Pro cigaretu
{
    name = "cigarette_luxury",
    label = "Luxusní cigareta",
    armor = 20,
    animation = "cigarette",
    duration = 15000,
    prop = {
        model = "prop_cs_ciggy_01",
        bone = 47419,
        coords = vector3(0.015, -0.009, 0.003),
        rotation = vector3(55.0, 0.0, 110.0)
    }
}

-- Pro snus
{
    name = "snus_mint",
    label = "Mentolový snus",
    armor = 12,
    animation = "snus_use",
    duration = 4000,
    prop = {
        model = "prop_food_bs_chips",
        bone = 18905,
        coords = vector3(0.13, 0.05, 0.02),
        rotation = vector3(-50.0, 16.0, 60.0)
    }
}
```

### 2. Přidejte export do server/main.lua
```lua
exports('cigarette_luxury', function(event, item, inventory, slot, data)
    if event == 'usingItem' then
        TriggerClientEvent('28_smoke:useItem', inventory.id, 'cigarette_luxury')
        return
    end
end)
```

### 3. Přidejte do ox_inventory/data/items.lua
```lua
['cigarette_luxury'] = {
    label = 'Luxusní cigareta',
    weight = 15,
    server = {
        export = '28_smoke.cigarette_luxury'
    }
}
```

## Troubleshooting

### Itemy nefungují:
1. Zkontrolujte, že je qb-core spuštěn
2. Zkontrolujte, že jste přidali itemy do `qb-core/shared/items.lua`
3. Zkontrolujte console pro chyby při startu scriptu
4. Použijte debug příkazy pro testování

### Debug:
- Nastavte `Config.Debug = true` v config.lua
- Restartujte script
- Sledujte console pro debug zprávy:
  ```
  [28_smoke] Registrován useable item: cigarette
  [28_smoke] Registrován useable item: doutnik
  [28_smoke] Registrován useable item: snus
  [28_smoke] Registrován useable item: snus_strong
  ```
- Použijte `/smoke_test` pro test funkčnosti

### Časté problémy:
- **Item se neodebere z inventáře**: Zkontrolujte, že máte správně nastavený `useable = true` v items.lua
- **Animace nefunguje**: Zkontrolujte console pro chyby s animacemi
- **Armor se nepřidá**: Zkontrolujte, že není armor již na maximu (100)

## Podpora
Pro podporu kontaktujte šlechy (autor scriptu).

## Changelog
- v1.2.0 - QBCore useable items systém (žádné exporty potřeba!)
- v1.1.0 - Přidána podpora ox_inventory exportů
- v1.0.0 - Standalone verze s pouze armor efekty
