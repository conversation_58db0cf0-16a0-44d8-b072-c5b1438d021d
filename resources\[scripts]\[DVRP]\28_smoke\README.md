# 28_smoke - Smoking & Snus System s Ox_Inventory

## Popis
Systém pro kouření cigaret a používání snusu s podporou ox_inventory, konfigurovatelnou animací a armor efekty.

## Funkce
- ✅ Kouření cigaret s animací a armor efekty
- ✅ Používání snusu s animací a armor efekty
- ✅ Konfigurovatelné animace pro každý item
- ✅ Pouze armor efekty (jednoduché a efektivní)
- ✅ Standalone progress bar
- ✅ Prop objekty během animace
- ✅ Podpora pro více typů cigaret a snusu
- ✅ Ox_inventory exporty pro použití itemů
- ✅ Debug režim pro testování
- ✅ Fallback příkazy pokud ox_inventory nefunguje

## Instalace

1. Zkopírujte složku `28_smoke` do `resources/[scripts]/[DVRP]/`
2. Přidejte do `server.cfg`:
   ```
   ensure 28_smoke
   ```
3. Přidejte itemy do ox_inventory (viz níže)
4. Restartujte server

## Závislosti
- **ox_inventory** (doporučeno pro použití itemů)
- Funguje i bez ox_inventory s příkazy

## Ox_Inventory Setup

### 1. Přidání itemů do ox_inventory

Přidejte do `ox_inventory/data/items.lua`:

```lua
['cigarette'] = {
    label = 'Cigareta',
    weight = 10,
    server = {
        export = '28_smoke.cigarette'
    }
},

['doutnik'] = {
    label = 'Doutník',
    weight = 15,
    server = {
        export = '28_smoke.doutnik'
    }
},

['snus'] = {
    label = 'Snus',
    weight = 5,
    server = {
        export = '28_smoke.snus'
    }
},

['snus_strong'] = {
    label = 'Silný snus',
    weight = 8,
    server = {
        export = '28_smoke.snus_strong'
    }
}
```

### 2. Použití itemů
- Klikněte pravým tlačítkem na item v inventáři
- Vyberte "Použít"
- Item se automaticky použije s animací a efekty

### 3. Fallback příkazy (pokud ox_inventory nefunguje)
- `/use_cigarette` - Použije základní cigaretu (+10 armor)
- `/use_doutnik` - Použije doutník (+15 armor)
- `/use_snus` - Použije základní snus (+5 armor)
- `/use_snus_strong` - Použije silný snus (+8 armor)

### 4. Debug příkazy (pouze když je Config.Debug = true)
- `/smoke_test` - Test příkaz
- `/smoke_armor [amount]` - Přidá armor
- `/smoke_give [item] [amount]` - Přidá item do inventáře

## Exporty pro Ox_Inventory

Script automaticky registruje tyto exporty:

```lua
-- V server/main.lua jsou definovány exporty:
exports('cigarette', function(event, item, inventory, slot, data)
    if event == 'usingItem' then
        TriggerClientEvent('28_smoke:useItem', inventory.id, 'cigarette')
        return
    end
end)

exports('doutnik', function(event, item, inventory, slot, data)
    if event == 'usingItem' then
        TriggerClientEvent('28_smoke:useItem', inventory.id, 'doutnik')
        return
    end
end)

exports('snus', function(event, item, inventory, slot, data)
    if event == 'usingItem' then
        TriggerClientEvent('28_smoke:useItem', inventory.id, 'snus')
        return
    end
end)

exports('snus_strong', function(event, item, inventory, slot, data)
    if event == 'usingItem' then
        TriggerClientEvent('28_smoke:useItem', inventory.id, 'snus_strong')
        return
    end
end)
```

## Konfigurace

### Základní nastavení
V `config.lua` můžete upravit:
- `Config.Debug` - zapne debug režim
- `Config.UseProgressBar` - použití progress baru

### Současné itemy v konfiguraci:
- **cigarette** - Základní cigareta (+10 armor)
- **doutnik** - Doutník (+15 armor)
- **snus** - Základní snus (+5 armor)
- **snus_strong** - Silný snus (+8 armor)

## Přidání nových itemů

### 1. Přidejte do config.lua
```lua
-- Pro cigaretu
{
    name = "cigarette_luxury",
    label = "Luxusní cigareta",
    armor = 20,
    animation = "cigarette",
    duration = 15000,
    prop = {
        model = "prop_cs_ciggy_01",
        bone = 47419,
        coords = vector3(0.015, -0.009, 0.003),
        rotation = vector3(55.0, 0.0, 110.0)
    }
}

-- Pro snus
{
    name = "snus_mint",
    label = "Mentolový snus",
    armor = 12,
    animation = "snus_use",
    duration = 4000,
    prop = {
        model = "prop_food_bs_chips",
        bone = 18905,
        coords = vector3(0.13, 0.05, 0.02),
        rotation = vector3(-50.0, 16.0, 60.0)
    }
}
```

### 2. Přidejte export do server/main.lua
```lua
exports('cigarette_luxury', function(event, item, inventory, slot, data)
    if event == 'usingItem' then
        TriggerClientEvent('28_smoke:useItem', inventory.id, 'cigarette_luxury')
        return
    end
end)
```

### 3. Přidejte do ox_inventory/data/items.lua
```lua
['cigarette_luxury'] = {
    label = 'Luxusní cigareta',
    weight = 15,
    server = {
        export = '28_smoke.cigarette_luxury'
    }
}
```

## Troubleshooting

### Itemy nefungují:
1. Zkontrolujte, že je ox_inventory spuštěn
2. Zkontrolujte, že jste přidali itemy do `ox_inventory/data/items.lua`
3. Zkontrolujte console pro chyby
4. Použijte debug příkazy pro testování

### Debug:
- Nastavte `Config.Debug = true` v config.lua
- Restartujte script
- Sledujte console pro debug zprávy
- Použijte `/smoke_test` pro test funkčnosti

## Podpora
Pro podporu kontaktujte šlechy (autor scriptu).

## Changelog
- v1.1.0 - Přidána podpora ox_inventory exportů
- v1.0.0 - Standalone verze s pouze armor efekty
