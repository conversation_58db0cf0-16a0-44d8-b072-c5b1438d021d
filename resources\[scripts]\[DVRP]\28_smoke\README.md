# 28_smoke - Smoking & Snus System

## Popis
Kompletní systém pro kouření cigaret a používání snusu s konfigurovatelnou animací a efekty.

## Funkce
- ✅ Kouření cigaret s animací a efekty
- ✅ Používání snusu s animací a efekty
- ✅ Konfigurovatelné animace pro každý item
- ✅ Konfigurovatelné efekty (zdraví, armor, stress, hlad, žízeň, stamina)
- ✅ Progress bar během používání
- ✅ Prop objekty během animace
- ✅ Podpora pro více typů cigaret a snusu
- ✅ Debug režim pro testování

## Instalace

1. Zkopírujte složku `28_smoke` do `resources/[scripts]/[DVRP]/`
2. Přidejte do `server.cfg`:
   ```
   ensure 28_smoke
   ```
3. Restartujte server

## Závislosti
- qb-core
- ox_lib (pro progress bar)
- ox_inventory (doporučeno)

## Konfigurace

### <PERSON><PERSON>lad<PERSON><PERSON> nastavení
V `config.lua` můžete upravit:
- `Config.Debug` - zapne debug režim
- `Config.UseProgressBar` - použití progress baru
- `Config.Framework` - "qb" nebo "esx"

### Kouření cigaret
```lua
Config.Smoking = {
    enabled = true,
    animations = {
        cigarette = {
            dict = "amb@world_human_smoking@male@male_a@enter",
            anim = "enter",
            flag = 49,
            duration = 10000
        }
    },
    items = {
        {
            name = "cigarette",
            label = "Cigareta",
            effects = {
                health = -5,
                armor = 10,
                stress = -20
            },
            animation = "cigarette",
            duration = 10000,
            prop = {
                model = "prop_cs_ciggy_01",
                bone = 47419,
                coords = vector3(0.015, -0.009, 0.003),
                rotation = vector3(55.0, 0.0, 110.0)
            }
        }
    }
}
```

### Snus systém
```lua
Config.Snus = {
    enabled = true,
    animations = {
        snus_use = {
            dict = "mp_player_intdrink",
            anim = "loop_bottle",
            flag = 49,
            duration = 3000
        }
    },
    items = {
        {
            name = "snus",
            label = "Snus",
            effects = {
                health = 5,
                armor = 5,
                stress = -15,
                stamina = 20
            }
        }
    }
}
```

## Přidání nových itemů

### Nová cigareta
```lua
{
    name = "cigarette_luxury",
    label = "Luxusní cigareta",
    effects = {
        health = -2,
        armor = 20,
        stress = -40,
        thirst = -2,
        hunger = -1
    },
    animation = "cigarette",
    duration = 15000,
    prop = {
        model = "prop_cs_ciggy_01",
        bone = 47419,
        coords = vector3(0.015, -0.009, 0.003),
        rotation = vector3(55.0, 0.0, 110.0)
    }
}
```

### Nový snus
```lua
{
    name = "snus_mint",
    label = "Mentolový snus",
    effects = {
        health = 8,
        armor = 8,
        stress = -30,
        stamina = 35,
        thirst = 5
    },
    animation = "snus_use",
    duration = 4000,
    prop = {
        model = "prop_food_bs_chips",
        bone = 18905,
        coords = vector3(0.13, 0.05, 0.02),
        rotation = vector3(-50.0, 16.0, 60.0)
    }
}
```

## Přidání itemů do databáze

Přidejte do `qb-core/shared/items.lua`:

```lua
-- Cigarety
cigarette = {
    name = 'cigarette',
    label = 'Cigareta',
    weight = 10,
    type = 'item',
    image = 'cigarette.png',
    unique = false,
    useable = true,
    shouldClose = true,
    combinable = nil,
    description = 'Klasická cigareta'
},

cigarette_premium = {
    name = 'cigarette_premium',
    label = 'Prémiová cigareta',
    weight = 10,
    type = 'item',
    image = 'cigarette_premium.png',
    unique = false,
    useable = true,
    shouldClose = true,
    combinable = nil,
    description = 'Prémiová cigareta s lepšími efekty'
},

-- Snus
snus = {
    name = 'snus',
    label = 'Snus',
    weight = 5,
    type = 'item',
    image = 'snus.png',
    unique = false,
    useable = true,
    shouldClose = true,
    combinable = nil,
    description = 'Švédský snus'
},

snus_strong = {
    name = 'snus_strong',
    label = 'Silný snus',
    weight = 5,
    type = 'item',
    image = 'snus_strong.png',
    unique = false,
    useable = true,
    shouldClose = true,
    combinable = nil,
    description = 'Silný snus s intenzivními efekty'
}
```

## Debug příkazy (pouze pro adminy)
- `/smoke_give [item] [amount]` - dá item
- `/smoke_effects [health] [armor] [stress]` - aplikuje efekty

## Podpora
Pro podporu kontaktujte DVRP tým.
